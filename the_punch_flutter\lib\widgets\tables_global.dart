import 'package:flutter/material.dart';

import '../helpers/color_helper.dart';

class CustomTable extends StatefulWidget {
  final List<DataColumn> columns;
  final List<DataRow> rows;
  final Function(DataRow) onRowClick;

  final double? headerHeight;
  final double? rowHeight;
  final bool stickyHeader;
  final double? headerFontSize;
  final double? cellFontSize;
  final Map<int, double> columnWidths;
  final double setMobileAt;
  final String? mobileTableTitle;
  final bool isHome;
  final bool allowMobile;
  final double tableMaxHeight;

  /// **Pagination parameter** (example):
  /// ```dart
  /// pagination: {
  ///   'active': true,
  ///   'rows': 25,
  ///   'page': 1, // NEW: Start on page #1
  /// }
  /// ```
  /// - `active`: Whether pagination is turned on (`true` / `false`).
  /// - `rows`: How many rows to show at a time (default `25`).
  /// - **`page`**: Which page index to start on (defaults to **0**).
  final Map<String, dynamic>? pagination;

  /// If true, the table height is determined by the total of
  /// header height + rows' heights rather than `tableMaxHeight`,
  /// so **all** rows are displayed with **no vertical scrolling**.
  final bool autoHeight;

  const CustomTable({
    Key? key,
    required this.columns,
    required this.rows,
    required this.onRowClick,
    this.headerHeight,
    this.rowHeight,
    this.stickyHeader = false,
    this.headerFontSize,
    this.cellFontSize,
    this.columnWidths = const {},
    this.setMobileAt = 768,
    this.mobileTableTitle = "Punch Cards Table",
    this.isHome = false,
    this.allowMobile = true,
    this.tableMaxHeight = 600.0,
    this.pagination = const {'active': false, 'rows': 25, 'page': 0},
    this.autoHeight = false,
  }) : super(key: key);

  @override
  _CustomTableState createState() => _CustomTableState();
}

/// Removes default overscroll indicators and optionally adds bounce
class CustomScrollBehavior extends ScrollBehavior {
  @override
  Widget buildViewportChrome(
      BuildContext context, Widget child, AxisDirection axisDirection) {
    return child; // no default overscroll indicator
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    return const BouncingScrollPhysics(); // optional bounce
  }
}

class _CustomTableState extends State<CustomTable> {
  /// Sorting
  bool _isAscending = true;
  int? _sortColumnIndex;
  late List<DataRow> _sortedRows;

  /// Colors for desktop header
  final Color headerBackgroundColor = Colors.transparent; // Material Red 600
  final Color headerTextColor = ColorHelper.thePunchDarkBlue();

  /// Only a horizontal scroll controller (since we'll remove vertical scroll if autoHeight = true)
  late ScrollController _horizontalScrollController;

  bool _needsHorizontalScrollbar = false;

  /// Pagination
  late bool _paginationActive;
  late int _rowsPerPage;

  // NEW: allow user to set the initial or updated page via pagination['page']
  int _currentPage = 0; // This is the "active" page we show

  @override
  void initState() {
    super.initState();
    // Initialize sorting
    _sortedRows = List<DataRow>.from(widget.rows);

    // Horizontal scroll controller
    _horizontalScrollController = ScrollController();

    // Initialize pagination
    _paginationActive = widget.pagination?['active'] ?? false;
    _rowsPerPage = widget.pagination?['rows'] ?? 25;

    // NEW: read 'page' from pagination; default 0 if not present
    _currentPage = widget.pagination?['page'] ?? 0;

    WidgetsBinding.instance
        .addPostFrameCallback((_) => _checkHorizontalScroll());
    _horizontalScrollController.addListener(_checkHorizontalScroll);
  }

  @override
  void dispose() {
    _horizontalScrollController.removeListener(_checkHorizontalScroll);
    _horizontalScrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant CustomTable oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the data changes, update your rows
    if (oldWidget.rows != widget.rows) {
      _sortedRows = List<DataRow>.from(widget.rows);
    }

    // If pagination changes, re-check
    _paginationActive = widget.pagination?['active'] ?? false;
    _rowsPerPage = widget.pagination?['rows'] ?? 25;

    // NEW: If user sets a new 'page' in pagination, reflect that
    final newPage = widget.pagination?['page'] ?? 0;
    if (newPage != _currentPage) {
      setState(() {
        _currentPage = newPage;
      });
    }

    WidgetsBinding.instance
        .addPostFrameCallback((_) => _checkHorizontalScroll());
  }

  /// ==================== PAGINATION LOGIC ====================
  List<DataRow> get _displayedRows {
    if (!_paginationActive) return _sortedRows;

    final startIndex = _currentPage * _rowsPerPage;
    final endIndex = startIndex + _rowsPerPage;
    final maxIndex =
        endIndex > _sortedRows.length ? _sortedRows.length : endIndex;
    return _sortedRows.sublist(startIndex, maxIndex);
  }

  int get _totalPages {
    if (!_paginationActive) return 1;
    return (_sortedRows.length / _rowsPerPage).ceil();
  }

  bool get _hasMultiplePages {
    if (!_paginationActive) return false;
    return _totalPages > 1;
  }

  void _goToPage(int page) {
    setState(() {
      _currentPage = page.clamp(0, _totalPages - 1);
    });
  }

  /// ==========================================================

  /// Checks if horizontal scrollbar is needed
  void _checkHorizontalScroll() {
    if (!_horizontalScrollController.hasClients) return;

    final horizontalExceeded =
        _horizontalScrollController.position.maxScrollExtent > 0;

    if (_needsHorizontalScrollbar != horizontalExceeded) {
      setState(() {
        _needsHorizontalScrollbar = horizontalExceeded;
      });
    }
  }

  /// Calculates each column's width based on `columnWidths` fractions
  double _calculateColumnWidth(int index, double totalWidth) {
    // If user gave a fraction for this column, use it
    if (widget.columnWidths.containsKey(index)) {
      return (widget.columnWidths[index]! * totalWidth).clamp(50.0, totalWidth);
    }

    // Else distribute leftover width evenly among columns
    final totalAssigned =
        widget.columnWidths.values.fold(0.0, (sum, w) => sum + w);
    final remainingWidthFraction = (1.0 - totalAssigned).clamp(0.0, 1.0);

    final remainingColumns = widget.columns.length - widget.columnWidths.length;
    final perColumnFraction = remainingColumns > 0
        ? remainingWidthFraction / remainingColumns
        : 1.0 / widget.columns.length;

    //return (perColumnFraction * totalWidth).clamp(50.0, totalWidth);
    return (perColumnFraction * totalWidth).clamp(50.0, totalWidth);
  }

  /// Sorting triggers
  void _onSort(int columnIndex) {
    setState(() {
      // If user clicks same column => flip ascending/descending
      if (_sortColumnIndex == columnIndex) {
        _isAscending = !_isAscending;
      } else {
        // Otherwise start ascending sort on new column
        _sortColumnIndex = columnIndex;
        _isAscending = true;
      }
      _sortRows(columnIndex);
    });
  }

  void _sortRows(int columnIndex) {
    _sortedRows.sort((a, b) {
      final aValue = _extractCellValue(a.cells[columnIndex]);
      final bValue = _extractCellValue(b.cells[columnIndex]);
      final compare = aValue.compareTo(bValue);
      return _isAscending ? compare : -compare;
    });
  }

  /// Gets textual value from a cell for sorting
  String _extractCellValue(DataCell cell) {
    final child = cell.child;
    if (child is Text) {
      return child.data ?? '';
    }
    // Fallback: Convert widget to string
    return child.toString();
  }

  /// For the desktop table to figure out how wide to draw itself
  double _calculateTotalTableWidth() {
    const double baseWidth = 1000.0;
    double totalWidth = 0;
    for (int i = 0; i < widget.columns.length; i++) {
      totalWidth += _calculateColumnWidth(i, baseWidth);
    }
    return totalWidth;
  }

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, constraints) {
          final double containerWidth = constraints.maxWidth;
          final bool isMobile =
              widget.allowMobile && (containerWidth < widget.setMobileAt);

          return Align(
            alignment: Alignment.centerLeft, // Aligns content to the left
            child:
                // SizedBox(
                //  width: containerWidth < 955 ? containerWidth : 955.0,
                //  child:
                Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                //  if (isMobile && widget.allowMobile) _buildMobileHeader(),
                if (isMobile && widget.allowMobile)
                  _buildVerticalTable()
                else
                  _buildDesktopTable(),
                if (_paginationActive && _hasMultiplePages)
                  _buildPaginationControls(),
              ],
            ),
            //),
          );
        },
      );

  /// ==================== MOBILE HEADER ====================
  Widget _buildMobileHeader() => Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        color: headerBackgroundColor,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.mobileTableTitle != null)
              Text(
                widget.mobileTableTitle!,
                style: TextStyle(
                  fontSize: widget.headerFontSize ?? 20,
                  fontWeight: FontWeight.bold,
                  color: headerTextColor,
                ),
              ),
            const SizedBox(height: 12),
            if (!widget.isHome) _buildMobileHeaderRowContent(),
          ],
        ),
      );

  Widget _buildMobileHeaderRowContent() {
    final List<String> columnNames = widget.columns.map((col) {
      if (col.label is Text) {
        return (col.label as Text).data ?? '';
      }
      return '';
    }).toList();

    String selectedColumnName = _sortColumnIndex != null
        ? columnNames[_sortColumnIndex!]
        : (columnNames.isNotEmpty ? columnNames[0] : '');

    return Row(
      children: [
        Expanded(
          child: DropdownButtonFormField<String>(
            value: selectedColumnName.isEmpty ? null : selectedColumnName,
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.white,
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
            ),
            items: columnNames
                .where((value) => value.isNotEmpty)
                .map<DropdownMenuItem<String>>(
                    (String value) => DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            value,
                            style: TextStyle(
                              color: Colors.black87,
                              fontWeight: FontWeight.w500,
                              fontSize: widget.headerFontSize ?? 16,
                            ),
                          ),
                        ))
                .toList(),
            dropdownColor: Colors.white,
            iconEnabledColor: Colors.black87,
            onChanged: (String? newValue) {
              if (newValue != null && newValue.isNotEmpty) {
                setState(() {
                  selectedColumnName = newValue;
                  final int newIndex = columnNames.indexOf(newValue);
                  _sortColumnIndex = newIndex;
                  _onSort(_sortColumnIndex!);
                });
              }
            },
          ),
        ),
        const SizedBox(width: 12),
        IconButton(
          icon: Icon(
            _isAscending ? Icons.arrow_upward : Icons.arrow_downward,
            color: Colors.white,
          ),
          onPressed: () {
            if (_sortColumnIndex != null) {
              setState(() {
                _isAscending = !_isAscending;
                _onSort(_sortColumnIndex!);
              });
            }
          },
        ),
      ],
    );
  }

  /// ==================== DESKTOP TABLE ====================
  Widget _buildDesktopTable() {
    final double tableWidth = _calculateTotalTableWidth();

    // Build the entire table in a Column: header + rows
    Widget tableContent = Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        _buildHeaderRow(),
        _displayedRows.isEmpty
            ? _buildEmptyTableMessage()
            : _buildDesktopBodyAllRows(),
      ],
    );

    // Wrap in horizontal scroll if needed
    if (_needsHorizontalScrollbar) {
      tableContent = SingleChildScrollView(
        controller: _horizontalScrollController,
        scrollDirection: Axis.horizontal,
        child: tableContent,
      );
      // Optionally wrap in a Scrollbar for better UX
      tableContent = Scrollbar(
        controller: _horizontalScrollController,
        thumbVisibility: true,
        thickness: 8.0,
        radius: const Radius.circular(20),
        child: tableContent,
      );
    }

    // The outer Container that controls total table height.
    // If autoHeight is true, there's no vertical constraint (no scroll).
    // Otherwise, we limit it with tableMaxHeight, which triggers vertical scroll if needed.
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
      // Use a specific width or let it be constrained by parent instead of infinity
      constraints: widget.autoHeight
          ? BoxConstraints(maxWidth: 1200)
          : BoxConstraints(
              maxHeight: widget.tableMaxHeight,
              maxWidth: tableWidth,
            ),

      // If autoHeight == false => a vertical scroll. Let's implement that here:
      child: widget.autoHeight
          ? tableContent
          : SingleChildScrollView(
              // This vertical scroll only applies if autoHeight == false
              child: tableContent,
            ),
    );
  }

  /// Builds a column of rows, each row is a "body row".
  /// We do NOT use ListView, so there's no vertical scroll if autoHeight = true.
  Widget _buildDesktopBodyAllRows() {
    return Column(
      children: _displayedRows.map((row) => _buildBodyRow(row)).toList(),
    );
  }

  /// Builds the colored header row for desktop
  Widget _buildHeaderRow() => Container(
        color: headerBackgroundColor,
        height: widget.headerHeight ?? 50.0,
        child: Row(
          children: widget.columns.asMap().entries.map((entry) {
            final columnIndex = entry.key;
            final column = entry.value;
            // Hardcoded 1000.0 matches _calculateColumnWidth's baseWidth
            final width = _calculateColumnWidth(columnIndex, 900);

            return Flexible(
              flex: 1,
              child: InkWell(
                onTap: () => _onSort(columnIndex),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          color: headerTextColor,
                          fontSize: widget.headerFontSize ?? 18,
                          fontWeight: FontWeight.bold,
                        ),
                        child: column.label,
                      ),
                      if (_sortColumnIndex == columnIndex)
                        Icon(
                          _isAscending
                              ? Icons.arrow_upward
                              : Icons.arrow_downward,
                          color: headerTextColor,
                          size: 16,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      );

  /// Builds each data row for the desktop table
  Widget _buildBodyRow(DataRow row) => InkWell(
        onTap: () => widget.onRowClick(row),
        child: Row(
          children: row.cells.asMap().entries.map((cellEntry) {
            final cellIndex = cellEntry.key;
            final cell = cellEntry.value;

            // Convert fraction to a "flex" for the Flexible widget
            final flexValue = widget.columnWidths.containsKey(cellIndex)
                ? (widget.columnWidths[cellIndex]! * 100).toInt()
                : 1;

            return Flexible(
              flex: 1,
              child: Container(
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
                child: DefaultTextStyle(
                  style: TextStyle(
                    fontSize: widget.cellFontSize ?? 16,
                    fontWeight: FontWeight.w500,
                    color: ColorHelper.thePunchDarkBlue(),
                  ),
                  child: cell.child,
                ),
              ),
            );
          }).toList(),
        ),
      );

  Widget _buildEmptyTableMessage() => Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        // Use constraints instead of infinite width
        constraints: const BoxConstraints(minWidth: 100),
        child: Center(
          child: Text(
            "TABLE CURRENTLY HAS NO DATA",
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );

  /// ==================== MOBILE TABLE ====================
  Widget _buildVerticalTable() {
    final rows = _displayedRows; // respects pagination

    if (widget.isHome) {
      // "Always expanded" style
      return rows.isEmpty
          ? _buildEmptyTableMessageMobile()
          : Expanded(
              child: ListView.builder(
                itemCount: rows.length,
                itemBuilder: (context, index) =>
                    _buildAlwaysExpandedMobileRow(rows[index], index),
              ),
            );
    } else {
      // Expandable (accordion) style
      return rows.isEmpty
          ? _buildEmptyTableMessageMobile()
          : Expanded(
              child: ScrollConfiguration(
                behavior: CustomScrollBehavior(),
                child: ListView.separated(
                  itemCount: rows.length,
                  separatorBuilder: (context, index) => Divider(
                    color: Colors.grey.shade300,
                    thickness: 1,
                    indent: 16,
                    endIndent: 16,
                  ),
                  itemBuilder: (context, index) =>
                      _buildExpandableMobileRow(rows[index]),
                ),
              ),
            );
    }
  }

  Widget _buildEmptyTableMessageMobile() => Container(
        margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            "TABLE CURRENTLY HAS NO DATA",
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );

  /// Accordion-style row
  Widget _buildExpandableMobileRow(DataRow row) {
    print(
        '${row.cells[0].child.toString()}   ${row.cells[1].child.toString()}   ${row.cells[2].child.toString()}   ${row.cells[3].child.toString()}   ${row.cells[4].child.toString()}   ${row.cells[5].child.toString()} ');

    final cells = row.cells;
    return ExpansionTile(
      initiallyExpanded: false,
      tilePadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      childrenPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (cells.isNotEmpty)
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: _getStatusColor(cells.isNotEmpty ? cells[0] : null),
                shape: BoxShape.circle,
              ),
            ),
          const SizedBox(width: 8),
            Expanded(
            child: Row(
              children: [
              // First column: limited characters, fixed width using flex
              Expanded(
                flex: 3,
                child: Text(
                (row.cells[0].child is Text)
                  ? (((row.cells[0].child as Text).data ?? '').length > 15
                    ? ((row.cells[0].child as Text).data!.substring(0, 12) + '...')
                    : (row.cells[0].child as Text).data ?? '')
                  : '',
                overflow: TextOverflow.ellipsis,
                ),
              ),
              // Second column: phone number, fixed width using flex
              Expanded(
                flex: 3,
                child: row.cells[1].child,
              ),
              // Spacer for a little separation if needed
              SizedBox(width: 8),
              // Third column: button, smaller flex
              Expanded(
                flex: 2,
                child: row.cells[5].child,
              ),
              ],
            ),
            ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // IconButton(
          //   icon: const Icon(Icons.visibility, color: Colors.blue),
          //   onPressed: () => widget.onRowClick(row),
          //   tooltip: 'View Details',
          // ),
          Icon(
            _isAscending ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
            color: Colors.black87,
          ),
        ],
      ),
      children: [
        Column(
          children: List.generate(
            cells.length > 2 ? cells.length - 2 : 0,
            (cellIndex) {
              final actualIndex = cellIndex + 2;
              final cell = cells[actualIndex];
              final columnLabel = widget.columns[actualIndex].label is Text
                  ? ((widget.columns[actualIndex].label as Text).data ?? '')
                  : '';

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 6.0),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 8),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.shade300,
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Text(
                        '$columnLabel:',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 9,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 6, horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _getCellText(cell),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade800,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// Always expanded row
  Widget _buildAlwaysExpandedMobileRow(DataRow row, int index) {
    final cells = row.cells;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => widget.onRowClick(row),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: widget.columns.asMap().entries.map((columnEntry) {
                      final cellIndex = columnEntry.key;
                      final column = columnEntry.value;
                      final cell = cells[cellIndex];
                      final columnLabel = column.label is Text
                          ? ((column.label as Text).data ?? '')
                          : '';

                      // If first column is "Live" indicator, skip label-value
                      if (widget.columns.isNotEmpty &&
                          widget.columns[0].label is Text &&
                          ((widget.columns[0].label as Text).data == 'Live') &&
                          cellIndex == 0) {
                        return const SizedBox.shrink();
                      }

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 6.0),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.shade300,
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              child: Text(
                                '$columnLabel:',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 9,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 6, horizontal: 8),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  _getCellText(cell),
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey.shade800,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.visibility, color: Colors.blue),
              onPressed: () => widget.onRowClick(row),
              tooltip: 'View Details',
            ),
          ],
        ),
      ),
    );
  }

  /// ==================== PAGINATION CONTROLS ====================
  Widget _buildPaginationControls() {
    // Don't show pagination if there's only 1 page
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Previous page button
          IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: _currentPage > 0
                ? () => _goToPage(_currentPage - 1)
                : null, // disable if we're on the first page
          ),

          // "Page X of Y"
          Text(
            'Page ${_currentPage + 1} of $_totalPages',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),

          // Next page button
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: _currentPage < _totalPages - 1
                ? () => _goToPage(_currentPage + 1)
                : null, // disable if we're on the last page
          ),
        ],
      ),
    );
  }

  /// ==========================================================

  /// Helpers
  String _getCellText(DataCell cell) {
    if (cell.child is Text) {
      return (cell.child as Text).data ?? '';
    }
    return '';
  }

  /// If first cell is an indicator container, get its color
  Color _getStatusColor(DataCell? cell) {
    if (cell == null) return Colors.grey;
    if (cell.child is Container) {
      final container = cell.child as Container;
      if (container.decoration is BoxDecoration) {
        final decoration = container.decoration as BoxDecoration;
        return decoration.color ?? Colors.grey;
      }
    }
    return Colors.grey;
  }
}
