import 'package:flutter/widgets.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';

import '../../../api/sync_model.dart';
import '../../../dataModel/data/alert.dart';
import '../../../dataModel/data/job_type.dart';
import '../../../dataModel/data/location.dart';
import '../../../dataModel/data/punch_card.dart';
import '../../../dataModel/data/schedule.dart';
import '../../../dataModel/data/user.dart';
import '../../../dataModel/data_model.dart';

import '../../../helpers/color_helper.dart';
import '../../../misc/bitmap_descriptor_extensions.dart';
import '../../../misc/collection_extensions.dart';
import '../../../misc/extensions.dart';

import '../../../widgets/date_time_picker.dart';
import '../../../widgets/decorated_text_field.dart';
import '../../../widgets/active_toggle.dart';
import '../../../widgets/search_text_field.dart';
import '../../../widgets/value_listenable_builder.dart';

import '../../view_model_mixin.dart';
import '../employees/employee_mixin.dart';
import '../my_scaffold.dart';
import 'widgets/filters_form.dart';

class PunchCardsPage extends StatelessWidget {
  const PunchCardsPage({super.key});

  @override
  Widget build(BuildContext context) => ChangeNotifierProvider<ViewModelPunch>(
        create: (context) => ViewModelPunch(),
        child: MyScaffold(
          title: AppLocalizations.of(context)!.punchCards,
          body: _Body(),
        ),
      );
}

class _Body extends StatelessWidget {
  static final ValueNotifier<bool> isEmployeePicked = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth * 0.90; // 90% of screen width
    final isMobile = screenWidth < 600;

    return Column(
      children: [
        Expanded(

          child: SingleChildScrollView(
            child: Center(
              child: SizedBox(
                width: containerWidth,
                child: Column(
                  children: [
                    _PunchSearch(),
                    const SizedBox(height: 16),
                    // Map and employee table in a row
                    ValueListenableBuilder<bool>(
                        valueListenable: isEmployeePicked,
                        builder: (context, isPicked, child) => !isPicked ? ( !isMobile?  Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left side - Employee table
                        Expanded(
                          flex:1,
                          child: _EmployeeTable(
                            onEmployeeSelected: (bool isSelected) {
                              isEmployeePicked.value = isSelected;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Right side - Map
                        Expanded(
                          flex:2,
                          child: _MapView(),
                        ),
                        SizedBox(width: 16)
                      ],
                    ):Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        // Left side - Employee table
                        Expanded(
                          flex:1,
                          child: _EmployeeTable(
                            onEmployeeSelected: (bool isSelected) {
                              isEmployeePicked.value = isSelected;
                            },
                          ),
                        ),
                        //const SizedBox(width: 16),
                        // Right side - Map
                        Expanded(
                          flex:2,
                          child: _MapView(),
                        ),
                        SizedBox(width: 16)
                      ],
                    ))
                    :
                    SizedBox(width: containerWidth, child: _PunchDetailsTable()),
                      ),

                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _MapView extends StatefulWidget {
  @override
  _MapViewState createState() => _MapViewState();
}

class _MapViewState extends State<_MapView> {
  GoogleMapController? _mapController;
  final Set<Marker> _markers = {};
  final Set<Circle> _circles = {};
  BitmapDescriptor? _redMarker;
  BitmapDescriptor? _grayMarker;
  BitmapDescriptor? _greenMarker;
  String _mapStyle = '';

  @override
  void initState() {
    super.initState();
    _initializeMap();
  }

  Future<void> _initializeMap() async {
    await _loadMapStyle();
    await _loadCustomMarkers();
  }

  Future<void> _loadMapStyle() async {
    try {
      _mapStyle = await rootBundle.loadString('assets/map_style.json');
    } catch (e) {
      print('Error loading map style: $e');
    }
  }

  Future<void> _loadCustomMarkers() async {
    try {
      _redMarker = await BitmapDescriptorExtension.fromIconData(
        Icons.where_to_vote_outlined,
        ColorHelper.thePunchRed(),
        45,
      );

      _grayMarker = await BitmapDescriptorExtension.fromIconData(
        Icons.watch_later,
        Colors.grey,
        45,
      );

      _greenMarker = await BitmapDescriptorExtension.fromIconData(
        Icons.person_pin_circle,
        Colors.green,
        45,
      );

      setState(() {});
    } catch (e) {
      print('Error loading custom markers: $e');
      _redMarker = BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      _grayMarker = BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
      _greenMarker = BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
    }
  }

  @override
  Widget build(BuildContext context) => Consumer<ViewModelPunch>(
      builder: (context, viewModel, child) {
        // Update markers and fit bounds when data changes
        _updateMarkers(viewModel);

        // Use a ValueListenableBuilder to listen for changes in the active filter
        return ValueListenableBuilder<ActiveToggleStatePunch>(
          valueListenable: viewModel.activeNotifier,
          builder: (context, activeState, _) => Card(
              elevation: 0,
              color: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5),
              ),
              margin: const EdgeInsets.symmetric(vertical: 20),
              child: SizedBox(
                height: 400,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(5),
                  child: Stack(
                    children: [
                      GoogleMap(
                        initialCameraPosition: const CameraPosition(
                          target: LatLng(37.7749, -122.4194), // Default location
                          zoom: 10,
                        ),
                        onMapCreated: (GoogleMapController controller) async {
                          _mapController = controller;
                          if (_mapStyle.isNotEmpty) {
                            await controller.setMapStyle(_mapStyle);
                          }
                          await _fitBounds();
                        },
                        markers: _markers,
                        circles: _circles,
                        myLocationEnabled: false,
                        zoomControlsEnabled: true,
                        mapType: MapType.normal,
                        onTap: (_) => viewModel.selectedEmployeeId = '',
                      ),
                      // Selected employee info overlay
                      if (viewModel.selectedEmployeeId.isNotEmpty &&
                          viewModel.employeeMap.containsKey(viewModel.selectedEmployeeId))
                        Positioned(
                          right: 60,
                          top: 10,
                          child: _buildSelectedEmployeeInfo(viewModel),
                        ),
                      // Map controls
                      Positioned(
                        top: 10,
                        right: 10,
                        child: Column(
                          children: [
                            // Zoom in button
                            FloatingActionButton(
                              heroTag: 'zoomIn',
                              mini: true,
                              onPressed: () async {
                                if (_mapController != null) {
                                  await _mapController!.animateCamera(CameraUpdate.zoomIn());
                                }
                              },
                              child: const Icon(Icons.add),
                            ),
                            const SizedBox(height: 8),
                            // Zoom out button
                            FloatingActionButton(
                              heroTag: 'zoomOut',
                              mini: true,
                              onPressed: () async {
                                if (_mapController != null) {
                                  await _mapController!.animateCamera(CameraUpdate.zoomOut());
                                }
                              },
                              child: const Icon(Icons.remove),
                            ),
                            const SizedBox(height: 8),
                            // Fit to markers button
                            FloatingActionButton(
                              heroTag: 'fitBounds',
                              mini: true,
                              backgroundColor: ColorHelper.thePunchRed(),
                              onPressed: () async {
                                await _fitBounds();
                              },
                              child: const Icon(Icons.fit_screen),
                            ),
                          ],
                        ),
                      ),
                      // Marker count indicator
                      Positioned(
                        bottom: 10,
                        left: 10,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Text(
                            '${_markers.length} location${_markers.length == 1 ? '' : 's'} shown',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )
        );
      },
    );

  Widget _buildSelectedEmployeeInfo(ViewModelPunch viewModel) {
    // Make sure we have a valid employee ID and it exists in the map
    if (viewModel.selectedEmployeeId.isEmpty ||
        !viewModel.employeeMap.containsKey(viewModel.selectedEmployeeId)) {
      return Container(); // Return empty container if no valid employee
    }

    final employee = viewModel.employeeMap[viewModel.selectedEmployeeId]!;
    final punchCards = viewModel.selectedPunchCards;
    final punchCard = punchCards.isNotEmpty ? punchCards.first : null;

    return Container(
      width: 200,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Text(
                'Completed',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 12,
                height: 12,
                decoration: const BoxDecoration(
                  color: Colors.orange,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 4),
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.5),
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // const Text('Employee'),
              // const SizedBox(width: 8),
              // const Text(
              //   '•',
              //   style: TextStyle(fontWeight: FontWeight.bold),
              // ),
              // const SizedBox(width: 8),
              Text(
                employee.name,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          if (punchCard != null) ...[
            const SizedBox(height: 4),
            Row(
              children: [
                const Text('In'),
                const SizedBox(width: 8),
                const Text(
                  '•',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),

                Text(
                  _formatDateTime(punchCard.clockedIn),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            if (punchCard.clockedOut != null) ...[
              const SizedBox(height: 4),
              Row(
                children: [
                  const Text('Out'),
                  const SizedBox(width: 8),
                  const Text(
                    '•',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 8),

                  Text(
                    _formatDateTime(punchCard.clockedOut!),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 4),
            Row(
              children: [
                const Text('Duration'),
                const SizedBox(width: 8),
                const Text(
                  '•',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(width: 8),

                Text(
                  _formatDuration(punchCard.duration ?? Duration.zero),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ],
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () async {
              if (punchCard != null) {
                await context.pushNamed(
                  '/punchCards/view',
              queryParameters: {'id': punchCard.punchCardLinkId},
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorHelper.thePunchAdminButtonBlue(),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              minimumSize: const Size(double.infinity, 36),
            ),
            child: const Text('View Full Card'),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final hour = dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour;
    final period = dateTime.hour >= 12 ? 'pm' : 'am';
    return '${dateTime.month}/${dateTime.day} ${hour}:${dateTime.minute.toString().padLeft(2, '0')} $period';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    return '${hours}h ${minutes}m';
  }

  Future<void> _updateMarkers(ViewModelPunch viewModel) async {
    // Store previous marker count to check if we need to update the map bounds
    final previousMarkerCount = _markers.length;

    _markers.clear();
    _circles.clear();

    // Track unique locations to avoid duplicate markers at the same position
    final locationPunchCards = <String, List<PunchCard>>{};
    final locationEmployees = <String, User>{};

    // First pass: Group punch cards by location
    for (final entry in viewModel.employeeMap.entries) {
      final employeeId = entry.key;
      final employee = entry.value;

      // Get all punch cards for this employee
      final employeePunchCards = viewModel.punchCards
          .where((pc) => pc.userId == employeeId)
          .toList();

      if (employeePunchCards.isEmpty) continue;

      // Check if any punch cards have location data
      for (final punchCard in employeePunchCards) {
        final location = viewModel.locationFromPunchCard(punchCard);
        if (location != null) {
          // Only add locations with valid coordinates
          if (location.latitude != null && location.longitude != null) {
            final locationKey = '${location.latitude},${location.longitude}';

            if (!locationPunchCards.containsKey(locationKey)) {
              locationPunchCards[locationKey] = [];
            }

            locationPunchCards[locationKey]!.add(punchCard);
            locationEmployees[locationKey] = employee;
          }
        }
      }
    }

    // Second pass: Create markers for each unique location
    for (final entry in locationPunchCards.entries) {
      final locationKey = entry.key;
      final punchCards = entry.value;
      final employee = locationEmployees[locationKey]!;

      // Get the location from the first punch card
      final location = viewModel.locationFromPunchCard(punchCards.first);
      if (location == null) continue;

      final position = LatLng(location.latitude, location.longitude);

      // Determine if any punch cards are live
      final hasLivePunchCard = punchCards.any((pc) => pc.clockedOut == null);

      // Choose marker icon based on status
      BitmapDescriptor icon;
      if (hasLivePunchCard) {
        icon = _greenMarker ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      } else {
        icon = _redMarker ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      }

      // Create marker with a more specific ID that includes the punch card ID
      // This will help us identify which punch card was clicked
      final punchCardId = punchCards.first.id;
      final marker = Marker(
        markerId: MarkerId('location-$locationKey-$punchCardId'),
        position: position,
        icon: icon,
        // infoWindow: InfoWindow(
        //   title: employee.name,
        //   snippet: hasLivePunchCard ? 'Active' : 'Completed',
        // ),
        onTap: () async {
          //print('PUNCH CARD ID${punchCardId}');
          // We can now use the punchCardId that we stored in the marker creation
          // This ensures we're using the exact punch card that was clicked
          for( final cards in viewModel.punchCards){
            if(cards.id == punchCardId){
              print('PUNCH CARD ID    list:  ${cards.id}      curent: ${punchCardId}');
            //  print(viewModel.)
            }
           // print('PUNCH CARD ID    list:  ${cards.id}      curent: ${punchCardId}');
          }
          if (punchCards.isNotEmpty) {
            // Get the specific punch card for this marker using the punchCardId
            // that we stored in the marker ID
         
            // Set the selected employee ID
            await viewModel.setSelectedEmployeeId(employee.id);

            // Force a rebuild of the UI to update the _buildSelectedEmployeeInfo
            setState(() {});

            if (_mapController != null) {
              // First zoom to the marker
              await _mapController!.animateCamera(
                CameraUpdate.newLatLngZoom(position, 14),
              );

              // Then apply an offset to show the info window better
              final screenCoordinate = await _mapController!.getScreenCoordinate(position);
              final offsetPoint = ScreenCoordinate(
                x: screenCoordinate.x,
                y: screenCoordinate.y - 100, // Offset upward to make room for the info window
              );
              final offsetLatLng = await _mapController!.getLatLng(offsetPoint);

              await _mapController!.animateCamera(
                CameraUpdate.newLatLng(offsetLatLng),
              );
            }
          }
        },
        zIndex: hasLivePunchCard ? 2 : 1, // Live markers on top
      );

      _markers.add(marker);

      // Add circle for geofence
      if (location.geoFenceRadius > 0) {
        _circles.add(
          Circle(
            circleId: CircleId('geofence-${location.id}'),
            center: position,
            radius: location.geoFenceRadius,
            fillColor: Colors.red.withOpacity(0.1),
            strokeColor: Colors.red.withOpacity(0.5),
            strokeWidth: 1,
          ),
        );
      }
    }

    // If markers count changed or we have markers but the map is at the default position,
    // update the map bounds to show all markers
    if (_mapController != null &&
        (_markers.isNotEmpty && (previousMarkerCount != _markers.length || previousMarkerCount == 0))) {
      await _fitBounds();
    }
  }

  Future<void> _fitBounds() async {
    if (_markers.isEmpty || _mapController == null) return;

    // Calculate bounds
    var minLat = 90.0;
    var maxLat = -90.0;
    var minLng = 180.0;
    var maxLng = -180.0;

    for (final marker in _markers) {
      final lat = marker.position.latitude;
      final lng = marker.position.longitude;

      minLat = lat < minLat ? lat : minLat;
      maxLat = lat > maxLat ? lat : maxLat;
      minLng = lng < minLng ? lng : minLng;
      maxLng = lng > maxLng ? lng : maxLng;
    }

    // Add padding
    final latPadding = (maxLat - minLat) * 0.1;
    final lngPadding = (maxLng - minLng) * 0.1;

    // If there's only one marker or markers are too close, ensure minimum zoom area
    if (maxLat - minLat < 0.01) {
      minLat -= 0.01;
      maxLat += 0.01;
    }

    if (maxLng - minLng < 0.01) {
      minLng -= 0.01;
      maxLng += 0.01;
    }

    final bounds = LatLngBounds(
      southwest: LatLng(minLat - latPadding, minLng - lngPadding),
      northeast: LatLng(maxLat + latPadding, maxLng + lngPadding),
    );

    try {
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(bounds, 50),
      );
    } catch (e) {
      // Fallback to a default position if bounds calculation fails
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(const LatLng(37.7749, -122.4194), 10),
      );
    }
  }
}

class _PunchSearch extends StatefulWidget {
  @override
  _PunchSearchState createState() => _PunchSearchState();
}

enum _TimePeriod {
  today,
  yesterday,
  thisWeek,
  lastWeek,
  thisMonth,
  lastMonth,
  all,
  custom,
  singleDay,
}

enum ActiveToggleStatePunch {
  both,
  active,
  inactive,
}

class _PunchSearchState extends State<_PunchSearch> {
  _TimePeriod timePeriod = _TimePeriod.thisWeek;
  final TextEditingController _fromController = TextEditingController();
  final TextEditingController _toController = TextEditingController();
  String? dateError;
  ActiveToggleStatePunch activeStatus = ActiveToggleStatePunch.both;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();
      // Example: current week's Monday-Sunday
      final currentWeekStart = now.subtract(Duration(days: now.weekday % 7));
      final currentWeekEnd = currentWeekStart.add(const Duration(days: 6));

      final viewModel = context.read<ViewModelPunch>();
      viewModel.setTimePeriod(currentWeekStart, currentWeekEnd);
      viewModel.activeNotifier.value = ActiveToggleStatePunch.both;

      final locale = Localizations.localeOf(context);
      _fromController.text = currentWeekStart.toFormattedDate(locale);
      _toController.text = currentWeekEnd.toFormattedDate(locale);
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Consumer<ViewModelPunch>(
      builder: (context, viewModel, child) =>
      FiltersForm(viewModel: viewModel)
      //Text("FOO")
    );
  }

  Widget buildTimePeriodDropdown(ViewModelPunch viewModel, BuildContext context) =>
    // We won't use "today.item1" or "today.item2" anymore. We'll do a date picker instead.
     DropdownButtonFormField<_TimePeriod>(
      icon: Icon(Icons.arrow_drop_down_rounded, color: ColorHelper.thePunchRed()),
      decoration: InputDecoration(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        filled: true,
        fillColor: Colors.grey[100],
        labelText: AppLocalizations.of(context)!.timePeriod,
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(
            color: ColorHelper.thePunchRed(),
            width: 2.0,
          ),
          borderRadius: BorderRadius.circular(10),
        ),
        labelStyle: TextStyle(
          color: ColorHelper.thePunchRed(),
        ),
      ),
      value: timePeriod,
      onChanged: (value) async {
        if (value == null) return;
        switch (value) {
          case _TimePeriod.today:
            // Change "Today" to also be a single date pick, defaulting to today's date
            final pickedDate = await showDatePicker(
              context: context,
              builder: (context, child) => Theme(
                data: ThemeData.light().copyWith(
                  colorScheme:
                      ColorScheme.light(primary: ColorHelper.thePunchRed()),
                ),
                child: child!,
              ),
              initialDate: DateTime.now(),
              firstDate: DateTime.utc(DateTime.now().year - 20),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (pickedDate != null) {
              await viewModel.setTimePeriod(pickedDate, pickedDate);
            }
            break;

          case _TimePeriod.yesterday:
            final yesterday = DateTime.now().subtract(const Duration(days: 1));
            await viewModel.setTimePeriod(yesterday, yesterday);
            break;

          case _TimePeriod.thisWeek:
            // e.g. Monday..Sunday of the current week
            final now = DateTime.now();
            final startOfWeek = now.subtract(Duration(days: now.weekday % 7));
            final endOfWeek = startOfWeek.add(const Duration(days: 6));
            await viewModel.setTimePeriod(startOfWeek, endOfWeek);
            break;

          case _TimePeriod.lastWeek:
            final nowLW = DateTime.now();
            final endOfPrevWeek =
                nowLW.subtract(Duration(days: nowLW.weekday % 7 + 1));
            final startOfPrevWeek = endOfPrevWeek.subtract(const Duration(days: 6));
            await viewModel.setTimePeriod(startOfPrevWeek, endOfPrevWeek);
            break;

          case _TimePeriod.thisMonth:
            final nowTM = DateTime.now();
            final firstDayTM = DateTime(nowTM.year, nowTM.month, 1);
            final lastDayTM = DateTime(nowTM.year, nowTM.month + 1, 0);
            await viewModel.setTimePeriod(firstDayTM, lastDayTM);
            break;

          case _TimePeriod.lastMonth:
            final nowLM = DateTime.now();
            final firstDayLM =
                DateTime(nowLM.year, nowLM.month - 1, 1); // last month, day=1
            final lastDayLM = DateTime(nowLM.year, nowLM.month, 0);
            await viewModel.setTimePeriod(firstDayLM, lastDayLM);
            break;

          case _TimePeriod.all:
            await viewModel.setTimePeriodAll();
            break;

          case _TimePeriod.custom:
            // The user will pick from/to manually
            break;

          case _TimePeriod.singleDay:
            // Show single day date picker
            final picked = await showDatePicker(
              context: context,
              builder: (context, child) => Theme(
                data: ThemeData.light().copyWith(
                  colorScheme:
                      ColorScheme.light(primary: ColorHelper.thePunchRed()),
                ),
                child: child!,
              ),
              initialDate: viewModel.start,
              firstDate: DateTime.utc(DateTime.now().year - 20),
              lastDate: DateTime.now().add(const Duration(days: 365)),
            );
            if (picked != null) {
              await viewModel.setTimePeriod(picked, picked);
            }
            break;
        }
        setState(() => timePeriod = value);
      },
      items: [
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.today,
          child: Text(AppLocalizations.of(context)!.today),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.yesterday,
          child: Text(AppLocalizations.of(context)!.yesterday),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.thisWeek,
          child: Text(AppLocalizations.of(context)!.thisWeek),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.lastWeek,
          child: Text(AppLocalizations.of(context)!.lastWeek),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.thisMonth,
          child: Text(AppLocalizations.of(context)!.thisMonth),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.lastMonth,
          child: Text(AppLocalizations.of(context)!.lastMonth),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.all,
          child: Text(AppLocalizations.of(context)!.allPunchCards),
        ),
        DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.custom,
          child: Text(AppLocalizations.of(context)!.customTimePeriod),
        ),
        const DropdownMenuItem<_TimePeriod>(
          value: _TimePeriod.singleDay,
          child: Text('Single Day'),
        ),
      ],
    );


  Widget buildActiveToggle(ViewModelPunch viewModel, BuildContext context) =>
      DropdownButtonFormField<ActiveToggleStatePunch>(
        icon: Icon(Icons.arrow_drop_down_rounded,
            color: ColorHelper.thePunchRed()),
        decoration: InputDecoration(
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          filled: true,
          fillColor: Colors.grey[100],
          labelText: 'Status',
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: ColorHelper.thePunchRed(), width: 2),
            borderRadius: BorderRadius.circular(10),
          ),
          labelStyle: TextStyle(
            color: ColorHelper.thePunchRed(),
          ),
        ),
        value: activeStatus,
        onChanged: (value) {
          setState(() => activeStatus = value ?? ActiveToggleStatePunch.both);
          viewModel.activeNotifier.value = activeStatus;
        },
        items: const [
          DropdownMenuItem<ActiveToggleStatePunch>(
            value: ActiveToggleStatePunch.both,
            child: Text("All"),
          ),
          DropdownMenuItem<ActiveToggleStatePunch>(
            value: ActiveToggleStatePunch.active,
            child: Text("Live"),
          ),
          DropdownMenuItem<ActiveToggleStatePunch>(
            value: ActiveToggleStatePunch.inactive,
            child: Text("Closed"),
          ),
        ],
      );

  Widget buildSingleDayPicker(BuildContext context, ViewModelPunch viewModel) {
    final locale = Localizations.localeOf(context);
    final dateString = viewModel.start.toLongFormattedDate(locale);

    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          builder: (context, child) => Theme(
            data: ThemeData.light().copyWith(
              colorScheme: ColorScheme.light(primary: ColorHelper.thePunchRed()),
            ),
            child: child!,
          ),
          initialDate: viewModel.start,
          firstDate: DateTime.utc(DateTime.now().year - 20),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (picked != null) {
          await viewModel.setTimePeriod(picked, picked);
          setState(() {});
        }
      },
      child: DecoratedText(
        text: dateString,
        labelText: "Single Day",
      ),
    );
  }

  Widget buildFrom(BuildContext context, ViewModelPunch viewModel) {
    final locale = Localizations.localeOf(context);

    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          builder: (context, child) => Theme(
            data: ThemeData.light().copyWith(
              colorScheme: ColorScheme.light(primary: ColorHelper.thePunchRed()),
            ),
            child: child!,
          ),
          initialDate: viewModel.start,
          firstDate: DateTime.utc(DateTime.now().year - 20),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (picked != null) {
          await viewModel.setTimePeriod(picked, viewModel.end);
        }
      },
      child: DecoratedText(
        text: viewModel.start.toLongFormattedDate(locale),
        labelText: AppLocalizations.of(context)!.from,
      ),
    );
  }

  Widget buildTo(BuildContext context, ViewModelPunch viewModel) {
    final locale = Localizations.localeOf(context);

    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          builder: (context, child) => Theme(
            data: ThemeData.light().copyWith(
              colorScheme: ColorScheme.light(primary: ColorHelper.thePunchRed()),
            ),
            child: child!,
          ),
          initialDate: viewModel.end,
          firstDate: DateTime.utc(DateTime.now().year - 20),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (picked != null) {
          await viewModel.setTimePeriod(viewModel.start, picked);
        }
      },
      child: DecoratedText(
        text: viewModel.end.toLongFormattedDate(locale),
        labelText: AppLocalizations.of(context)!.to,
      ),
    );
  }

  @override
  void dispose() {
    _fromController.dispose();
    _toController.dispose();
    super.dispose();
  }
}

class _EmployeeTable extends StatefulWidget {
  final ValueChanged<bool> onEmployeeSelected;

  const _EmployeeTable({super.key, required this.onEmployeeSelected});

  @override
  _EmployeeTableState createState() => _EmployeeTableState();
}

class _EmployeeTableState extends State<_EmployeeTable> {
  int _sortColumn = 0;
  bool _isAscending = false;
  final _scrollController = ScrollController();
  String? _highlightedEmployeeId;

  // Alert colors based on alert types
  final Map<String, Color> alertColors = {
    Alert.earlyPunchInId: const Color(0xFFFFD500), // Yellow
    Alert.latePunchInId: const Color(0xFFFFD500), // Yellow
    Alert.earlyPunchOutId: const Color(0xFFFF6F00), // Orange
    Alert.latePunchOutId: const Color(0xFFFF6F00), // Orange
    Alert.outsideGeofenceId: const Color(0xFFFF0000), // Red
    Alert.activeBreach: const Color(0xFFFF0000), // Red
  };

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, layout) {
          final screenWidth = MediaQuery.of(context).size.width;
          final containerWidth = screenWidth * 0.90;

          return SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: SizedBox(
              width: containerWidth,
              child: Card(
                elevation: 0,
                color: Colors.white, // White background as per requirement
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
                child: Consumer<ViewModelPunch>(
                  builder: (context, viewModel, child) => ValueListenableBuilder2(
                    valueListenable1: viewModel.searchNotifier,
                    valueListenable2: viewModel.activeNotifier,
                    builder: (context, _, __) => SingleChildScrollView(
                      controller: _scrollController,
                      scrollDirection: Axis.horizontal,
                      child: viewModel.employeeMap.isNotEmpty
                          ? DataTable(
                            decoration:  BoxDecoration(
                                color: Colors.white, // White background
                                borderRadius: BorderRadius.circular(20),

                            ) ,
                              showCheckboxColumn: false,
                              dataRowMaxHeight: 50,
                              dataRowMinHeight:50,
                              showBottomBorder: false,
                              dataRowColor: MaterialStateColor.resolveWith(
                                (states) => ui.Color.fromARGB(255, 187, 255, 187), // White row background
                              ),
                              dividerThickness: 0.0000001,
                              headingTextStyle: TextStyle(color: Colors.black, fontSize: 14),
                              dataTextStyle: TextStyle(color: Colors.black, fontSize: 13),
                              headingRowColor: MaterialStateColor.resolveWith(
                                (states) => Colors.white, // White header background
                              ),
                              columns: getColumns(viewModel),
                              rows: getRows(viewModel),
                            )
                          : const Text(
                              'No employees have punched in or out for the dates you specified above.',
                              textAlign: TextAlign.justify,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                color: Color.fromARGB(255, 196, 50, 50),
                              ),
                            ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      );

  List<DataColumn> getColumns(ViewModelPunch viewModel) => [
        const DataColumn(
          label: Text('Live', style:TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
        ),
        DataColumn(
          label: buildSortableColumn(
            AppLocalizations.of(context)!.name,
            0,
          ),
        ),
        DataColumn(
          label: buildSortableColumn(
            'Time',
            1,
          ),
        ),
        DataColumn(
          label: buildSortableColumn(
            AppLocalizations.of(context)!.alerts,
            2,
          ),
        ),
      ];

  Widget buildSortableColumn(String label, int columnIndex) => GestureDetector(
        onTap: () => onSort(columnIndex),
        onDoubleTap: resetToDefault,
        child: Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Colors.black, // Black text for column headers
                fontWeight: FontWeight.bold,
                fontSize: 14
              ),
            ),
            if (_sortColumn == columnIndex)
              Icon(
                _isAscending
                    ? Icons.arrow_upward_rounded
                    : Icons.arrow_downward_rounded,
                color: Colors.black,
                size: 16,
              ),
          ],
        ),
      );

  List<DataRow> getRows(ViewModelPunch viewModel) {
    if (!viewModel.isRefreshed || viewModel.employeeMap.isEmpty) return [];

    final employees = viewModel.employeeMap.values.toList();
    final hoursMap = viewModel.hoursMap;
    final alertCountMap = viewModel.alertCountMap;
    final liveStatusMap = _getLiveStatus(viewModel);
    final alertsMap = _getEmployeeAlerts(viewModel);

    // Sort employees
    if (_isAscending) {
      switch (_sortColumn) {
        case 0:
          employees.sort((a, b) => a.name.compareTo(b.name));
          break;
        case 1:
          employees.sort((a, b) => (hoursMap[a.id] ?? Duration.zero)
              .compareTo(hoursMap[b.id] ?? Duration.zero));
          break;
        case 2:
          employees.sort((a, b) =>
              (alertCountMap[a.id] ?? 0).compareTo(alertCountMap[b.id] ?? 0));
          break;
      }
    } else {
      switch (_sortColumn) {
        case 0:
          employees.sort((b, a) => a.name.compareTo(b.name));
          break;
        case 1:
          employees.sort((b, a) => (hoursMap[a.id] ?? Duration.zero)
              .compareTo(hoursMap[b.id] ?? Duration.zero));
          break;
        case 2:
          employees.sort((b, a) =>
              (alertCountMap[a.id] ?? 0).compareTo(alertCountMap[b.id] ?? 0));
          break;
      }
    }

    return employees.map((e) => DataRow(
      selected: viewModel.selectedEmployeeId == e.id,
      color: MaterialStateProperty.resolveWith<Color?>((states) {
        if (states.contains(MaterialState.selected)) {
          return const Color.fromARGB(255, 187, 255, 187); // Light green for selected
        }
        if (_highlightedEmployeeId == e.id) {
          return ui.Color.fromARGB(255, 180, 255, 180); // Light gray highlight
        }
        return Colors.white; // White background for non-selected rows
      }),
      onSelectChanged: (selected) async {
        if (selected == true) {
          setState(() => _highlightedEmployeeId = e.id);
          await viewModel.setSelectedEmployeeId(e.id);
          setState(() {});
          widget.onEmployeeSelected(true); //
        }
      },
      cells: [
        DataCell(_buildLiveStatusCircle(liveStatusMap[e.id] ?? false)),
        DataCell(Text(e.name)),
        DataCell(Text(hoursMap[e.id]?.toFormatted ?? '1hr')), // Display "1hr" as in the image
        DataCell(_buildAlertIndicators(alertsMap[e.id] ?? [])),
      ],
    )).toList();

}

  Map<String, bool> _getLiveStatus(ViewModelPunch viewModel) {
    final liveStatusMap = <String, bool>{};
    viewModel.employeeMap.forEach((employeeId, _) {
      final hasLiveTicket = viewModel.punchCards
          .where((punchCard) => punchCard.userId == employeeId)
          .any((punchCard) => punchCard.clockedOut == null);
      liveStatusMap[employeeId] = hasLiveTicket;
    });
    return liveStatusMap;
  }

  Map<String, List<String>> _getEmployeeAlerts(ViewModelPunch viewModel) {
    final alertsMap = <String, List<String>>{};

    viewModel.employeeMap.forEach((employeeId, _) {
      final employeeAlerts = <String>[];

      // Get all alerts for this employee's punch cards
      final employeePunchCards = viewModel.punchCards
          .where((punchCard) => punchCard.userId == employeeId);

      for (final punchCard in employeePunchCards) {
        final punchCardAlerts = viewModel.allAlertsMap[punchCard.id] ?? [];
        for (final alert in punchCardAlerts) {
          employeeAlerts.add(alert.alertTypeId);
        }
      }

      alertsMap[employeeId] = employeeAlerts;
    });

    return alertsMap;
  }

  Widget _buildLiveStatusCircle(bool hasLiveTicket) => hasLiveTicket
    ? Icon(Icons.where_to_vote, color: ColorHelper.thePunchAccentRed(), size: 25)
    : Icon(Icons.where_to_vote_outlined, color: Colors.grey[200], size: 25);

  Widget _buildAlertIndicators(List<String> alertTypeIds) {
    // Get unique alert types
    final uniqueAlertTypes = alertTypeIds.toSet();
    if (uniqueAlertTypes.isEmpty) {
      return const SizedBox(); // No alerts
    }

    // Alert colors based on alert types
    final Map<String, Color> alertColors = {
      Alert.earlyPunchInId: const Color(0xFFFFD500), // Yellow
      Alert.latePunchInId: const Color(0xFFFFD500), // Yellow
      Alert.earlyPunchOutId: const Color(0xFFFF6F00), // Orange
      Alert.latePunchOutId: const Color(0xFFFF6F00), // Orange
      Alert.outsideGeofenceId: const Color(0xFFFF0000), // Red
      Alert.activeBreach: const Color(0xFFFF0000), // Red
    };

    // Create a list of colored dots based on alert types
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: uniqueAlertTypes.map((alertTypeId) {
        final color = alertColors[alertTypeId] ?? Colors.grey;
        return Container(
          width: 16,
          height: 16,
          margin: const EdgeInsets.only(right: 4),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        );
      }).toList(),
    );
  }

  void onSort(int columnIndex) {
    setState(() {
      if (_sortColumn == columnIndex) {
        _isAscending = !_isAscending;
      } else {
        _sortColumn = columnIndex;
        _isAscending = true;
      }
    });
  }

  void resetToDefault() {
    setState(() {
      _sortColumn = 0;
      _isAscending = false;
    });
  }
}

class _PunchDetailsTable extends StatefulWidget {
  @override
  _PunchDetailsTableState createState() => _PunchDetailsTableState();
}

class _PunchDetailsTableState extends State<_PunchDetailsTable> {
  Timer? _timer;
  int _sortColumn = 2;
  bool _isAscending = false;
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Update "live" durations every second
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => LayoutBuilder(
        builder: (context, layout) {
          final screenWidth = MediaQuery.of(context).size.width;
          final containerWidth = screenWidth * 0.90;

          return Card(
            elevation: 0,
            color: Colors.white, // White background as per requirement

            child: Consumer<ViewModelPunch>(
              builder: (context, viewModel, child) {
                if (!viewModel.isRefreshed ||
                    viewModel.selectedEmployeeId.isEmpty ||
                    viewModel.groupedPunchCards.isEmpty) {
                  return Container();
                }
                return SizedBox(
                  width: containerWidth,
                  child: SingleChildScrollView(

                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(
                      vertical: 20,
                      horizontal: 16,
                    ),
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back),
                            onPressed: () {
                              _Body.isEmployeePicked.value = false;
                            },
                          ),
                          const SizedBox(width: 8),
                          Text(
                            viewModel.employeeMap[viewModel.selectedEmployeeId]?.name ?? 'Unknown Employee',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                        DataTable(
                          headingTextStyle: TextStyle(color: Colors.black),
                      showCheckboxColumn: false,
                      decoration: BoxDecoration(
                        color: Colors.white, // White background
                        borderRadius: BorderRadius.circular(20),
                      ),
                      showBottomBorder: false,
                      dividerThickness: 0.00000001,
                      dataTextStyle: TextStyle(color: Colors.black, fontSize: 13),
                      headingRowColor: MaterialStateColor.resolveWith(
                        (states) => Colors.white,
                      ),
                      sortColumnIndex: _sortColumn,
                      sortAscending: _isAscending,
                      columns: getColumns(viewModel, context),
                      rows: getRows(viewModel, context),
                    )
                      ]
                    ),
                  ),
                );
              },
            ),
          );
        },
      );

  List<DataColumn> getColumns(ViewModelPunch viewModel, BuildContext context) => [
        DataColumn(
          label: SizedBox(
            width: 64,
            child: Text(
              AppLocalizations.of(context)!.jobType,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ),
        DataColumn(
          label: SizedBox(
            width: 64,
            child: Text(
              AppLocalizations.of(context)!.location,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ),
        DataColumn(
          label: SizedBox(
            width: 64,
            child: Text(
              AppLocalizations.of(context)!.clockIn,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ),
        DataColumn(
          label: SizedBox(
            width: 75,
            child: Text(
              "Punch Out",
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ),
        DataColumn(
          label: SizedBox(
            width: 64,
            child: Text(
              AppLocalizations.of(context)!.duration,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ),
        const DataColumn(
          label: SizedBox(
            width: 64,
            child: Text(
              'Alerts',
              style: TextStyle(fontSize: 14),
            ),
          ),
        ),
        const DataColumn(
          label: SizedBox(
            width: 64,
            child: Text(
              'Tasks',
              style: TextStyle(fontSize: 14),
            ),
          ),
        ),
      ];

  List<DataRow> getRows(ViewModelPunch viewModel, BuildContext context) {
    if (!viewModel.isRefreshed || viewModel.groupedPunchCards.isEmpty) {
      return [];
    }

    var groups = viewModel.groupedPunchCards.toList();

    // Helper functions for sorting
    int compareString(String a, String b) =>
        _isAscending ? a.compareTo(b) : b.compareTo(a);
    int compareDates(DateTime? a, DateTime? b) {
      if (a == null && b == null) return 0;
      if (a == null) return _isAscending ? 1 : -1;
      if (b == null) return _isAscending ? -1 : 1;
      return _isAscending ? a.compareTo(b) : b.compareTo(a);
    }

    int compareDuration(Duration a, Duration b) =>
        _isAscending ? a.compareTo(b) : b.compareTo(a);
    int compareInt(int a, int b) =>
        _isAscending ? a.compareTo(b) : b.compareTo(a);

    // Sort the groups based on the selected column
    groups.sort((a, b) {
      final totalDurationA = _calculateDisplayedDuration(a);
      final totalDurationB = _calculateDisplayedDuration(b);

      switch (_sortColumn) {
        case 0:
          // jobType
          return compareString(a.jobTypeName, b.jobTypeName);
        case 1:
          // location
          return compareString(a.locationName, b.locationName);
        case 2:
          // earliestClockIn
          return compareDates(a.earliestClockIn, b.earliestClockIn);
        case 3:
          // latestClockOut
          return compareDates(a.latestClockOut, b.latestClockOut);
        case 4:
          // duration
          return compareDuration(totalDurationA, totalDurationB);
        case 5:
          // alerts
          return compareInt(a.alertCount, b.alertCount);
        case 6:
          // tasks
          return compareInt(a.taskCount, b.taskCount);
        default:
          return 0;
      }
    });

    final locale = Localizations.localeOf(context);

    return groups.map((group) {
      // If job type is "Travel Time", we show "x" for punch in / punch out.
      final isTravelTime = group.jobTypeName.toLowerCase() == 'travel time';

      final totalDuration = _calculateDisplayedDuration(group);
      final durationWidget = group.isLive
          ? _durationToText(totalDuration, live: true)
          : _durationToText(totalDuration, live: false);

      // Decide what to display for clock in / clock out
      final clockInText = isTravelTime
          ? '-'
          : group.earliestClockIn.toFormattedDateTime(locale);

      final clockOutText = isTravelTime
          ? '-'
          : group.isLive
              ? 'LIVE'
              : group.latestClockOut?.toFormattedDateTime(locale) ?? '';

      return DataRow(
        onSelectChanged: (selected) {
          if (selected == true) {
            context.pushNamed(
              '/punchCards/view',
              queryParameters: {'id': group.punchCardLinkId},
            );
          }
        },
        cells: [
          DataCell(Text(group.jobTypeName)),
          DataCell(Text(group.locationName)),
          DataCell(Text(clockInText)),
          DataCell(
            isTravelTime
                ? Text(clockOutText)
                : group.isLive
                    ? Text(
                        clockOutText,
                        style: const TextStyle(
                          color: Colors.green,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : Text(clockOutText),
          ),
          DataCell(durationWidget),
          DataCell(_buildAlertIndicators(group.alertTypes)),
          DataCell(Text(group.taskCount.toString())),
        ],
      );
    }).toList();
  }
  Widget _buildAlertIndicators(List<String> alertTypeIds) {
    // Get unique alert types
    final uniqueAlertTypes = alertTypeIds.toSet();
    if (uniqueAlertTypes.isEmpty) {
      return const SizedBox(); // No alerts
    }

    // Alert colors based on alert types
    final Map<String, Color> alertColors = {
      Alert.earlyPunchInId: const Color(0xFFFFD500), // Yellow
      Alert.latePunchInId: const Color(0xFFFFD500), // Yellow
      Alert.earlyPunchOutId: const Color(0xFFFF6F00), // Orange
      Alert.latePunchOutId: const Color(0xFFFF6F00), // Orange
      Alert.outsideGeofenceId: const Color(0xFFFF0000), // Red
      Alert.activeBreach: const Color(0xFFFF0000), // Red
    };

    // Create a list of colored dots based on alert types
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: uniqueAlertTypes.map((alertTypeId) {
        final color = alertColors[alertTypeId] ?? Colors.grey;
        return Container(
          width: 16,
          height: 16,
          margin: const EdgeInsets.only(right: 4),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        );
      }).toList(),
    );
  }

  Duration _calculateDisplayedDuration(_PunchCardGroup group) {
    // closedDuration is the sum of ended punchcards
    Duration total = group.closedDuration;
    // If "live", add the time from clockIn to now
    if (group.isLive) {
      for (final openIn in group.openClockIns) {
        total += DateTime.now().difference(openIn);
      }
    }
    return total;
  }

  Widget _durationToText(Duration duration, {bool live = false}) {
    if (!live) {
      return Text(duration.toFormatted);
    } else {
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      final seconds = duration.inSeconds % 60;
      final text = '${hours}h ${minutes}m ${seconds}s';
      return Text(
        text,
        style: const TextStyle(color: Colors.green),
      );
    }
  }

  void onSort(int columnIndex, bool ascending) {
    setState(() {
      _sortColumn = columnIndex;
      _isAscending = ascending;
    });
  }
}


class _PunchCardGroup {
  final String punchCardLinkId;
  final String firstPunchCardId;
  final Duration closedDuration;
  final List<DateTime> openClockIns;
  final bool isLive;
  final String jobTypeName;
  final String locationName;

  final DateTime earliestClockIn;
  final DateTime? latestClockOut;
  final int alertCount;
  final List<String> alertTypes;
  final int taskCount;

  _PunchCardGroup({
    required this.punchCardLinkId,
    required this.firstPunchCardId,
    required this.closedDuration,
    required this.openClockIns,
    required this.isLive,
    required this.jobTypeName,
    required this.locationName,
    required this.earliestClockIn,
    required this.latestClockOut,
    required this.alertCount,
    required this.alertTypes,
    required this.taskCount,
  });
}

class ViewModelPunch extends ChangeNotifier with ViewModelMixin, EmployeeMixin {
  Iterable<PunchCard> punchCards = [];
  var employeeMap = <String, User>{};
  var hoursMap = <String, Duration>{};
  var alertCountMap = <String, int>{};

  var activeNotifier = ValueNotifier(ActiveToggleStatePunch.active);
  var searchNotifier = ValueNotifier('');

  // We'll set these in setTimePeriod so "single day" works
  DateTime start = DateTime.now().dateOnly;
  DateTime end = DateTime.now().dateOnly;

  String selectedEmployeeId = '';
  Iterable<PunchCard> selectedPunchCards = [];
  var jobTypeMap = <String, JobType>{};
  var locationMap = <String, Location>{};
  var scheduleMap = <String, Schedule>{};
  var selectedAlertMap = <String, Iterable<Alert>>{};
  var allAlertsMap = <String, Iterable<Alert>>{};

  List<_PunchCardGroup> groupedPunchCards = [];

  ViewModelPunch() {
    addListenables([
      DataModel().punchCardModel,
      DataModel().userModel,
      DataModel().alertModel,
      DataModel().locationModel,
      DataModel().jobTypeModel,
      DataModel().scheduleModel,
      activeNotifier,
      searchNotifier,
    ]);

    unawaited(SyncModel().sync());
    unawaited(refresh());
  }

  @override
  Future<void> refresh() async {
    // Get *all* punch cards
    final all = await DataModel().punchCardModel.all;

    // Filter by date overlap with [start, end]
    // If clockedOut == null, interpret as "still open" so we use DateTime.now() for its end
    punchCards = all.where((pc) {
      final out = pc.clockedOut ?? DateTime.now();
      // Overlaps if pc.clockedIn <= end && out >= start
      return pc.clockedIn.isBefore(end) && out.isAfter(start);
    });

    // Filter by active/inactive
    switch (activeNotifier.value) {
      case ActiveToggleStatePunch.active:
        punchCards = punchCards.where((pc) => pc.clockedOut == null);
        break;
      case ActiveToggleStatePunch.inactive:
        punchCards = punchCards.where((pc) => pc.clockedOut != null);
        break;
      case ActiveToggleStatePunch.both:
        // no filter
        break;
    }

    // Collect employees who appear
    final userIds = punchCards.map((pc) => pc.userId).toSet();
    var employees = await DataModel().userModel.getByIds(userIds);

    // Filter by search
    final query = searchNotifier.value.trim().toLowerCase();
    if (query.isNotEmpty) {
      employees = employees.where((emp) =>
          emp.name.toLowerCase().contains(query));
    }
    employeeMap = {for (final e in employees) e.id: e};

    // Hours
    hoursMap = {
      for (final e in employees)
        e.id: punchCards
            .where((pc) => pc.userId == e.id)
            .map((pc) => pc.duration ?? Duration.zero)
            .fold(Duration.zero, (a, b) => a + b)
    };

    // Alerts
    final pcIds = punchCards.map((pc) => pc.id).toSet();
    final alerts = await DataModel().alertModel.getActiveByPunchCardIds(pcIds);
    alertCountMap = {
      for (final e in employees)
        e.id: alerts.where((al) => al.userId == e.id).length
    };

    // Populate allAlertsMap with alerts for all punch cards
    allAlertsMap = {};
    for (final pc in punchCards) {
      final punchCardAlerts = alerts.where((al) => al.punchCardId == pc.id);
      allAlertsMap[pc.id] = punchCardAlerts;
    }

    // Load job types, schedules, locations
    final jobTypeIds = punchCards.map((pc) => pc.jobTypeId).toSet();
    final jobTypes = await DataModel().jobTypeModel.getByIds(jobTypeIds);
    jobTypeMap = {for (final jt in jobTypes) jt.id: jt};

    final scheduleIds = punchCards
        .where((pc) => pc.scheduleId != null)
        .map((pc) => pc.scheduleId!)
        .toSet();
    final schedules = await DataModel().scheduleModel.getByIds(scheduleIds);
    scheduleMap = {for (final s in schedules) s.id: s};

    final locationIds = punchCards
        .where((pc) => pc.locationId != null)
        .map((pc) => pc.locationId!)
        .toSet();
    final locs = await DataModel().locationModel.getByIds(locationIds);
    locationMap = {for (final l in locs) l.id: l};

    // If we already have a "selected" employee, build details for them
    if (employeeMap.containsKey(selectedEmployeeId)) {
      selectedPunchCards =
          punchCards.where((pc) => pc.userId == selectedEmployeeId);

      // Alerts for that user's punchcards
      final selectedPCIds = selectedPunchCards.map((pc) => pc.id).toSet();
      final selectedAlerts =
          alerts.where((al) => selectedPCIds.contains(al.punchCardId));
      selectedAlertMap = {
        for (final pcId in selectedPCIds)
          pcId: selectedAlerts.where((al) => al.punchCardId == pcId)
      };

      groupedPunchCards = _groupPunchCardsByLinkId(selectedPunchCards);
    } else {
      selectedPunchCards = [];
      selectedAlertMap = {};
      groupedPunchCards = [];
    }

    notifyListeners();
  }

  List<_PunchCardGroup> _groupPunchCardsByLinkId(Iterable<PunchCard> punchCards) {
    // Group by punchCardLinkId
    final grouped = punchCards.groupBy((pc) => pc.punchCardLinkId ?? pc.id);
    final result = <_PunchCardGroup>[];

    for (final entry in grouped.entries) {
      final linked = entry.value.toList();
      linked.sort((a, b) => a.clockedIn.compareTo(b.clockedIn));

      // Calculate earliest clockIn and latest clockOut
      final earliest = linked.first.clockedIn;
      final latest = linked.fold<DateTime?>(
          null,
          (latest, pc) => latest == null
              ? pc.clockedOut
              : (pc.clockedOut != null && pc.clockedOut!.isAfter(latest))
                  ? pc.clockedOut
                  : latest);

      // Calculate if any are still "live" (no clockOut)
      final isLive = linked.any((pc) => pc.clockedOut == null);

      // Calculate total duration for completed punch cards
      final closedDuration = linked
          .where((pc) => pc.clockedOut != null)
          .map((pc) => pc.clockedOut!.difference(pc.clockedIn))
          .fold(Duration.zero, (a, b) => a + b);

      // Collect open clockIns for live calculation
      final openClockIns = linked
          .where((pc) => pc.clockedOut == null)
          .map((pc) => pc.clockedIn)
          .toList();

      // Count alerts for these punchcards
      final alertsCount = linked.fold<int>(0, (count, pc) => count + (selectedAlertMap[pc.id]?.length ?? 0));

      // Collect alert types
      final alertTypes = <String>[];
      for (final pc in linked) {
        final alerts = selectedAlertMap[pc.id] ?? [];
        for (final alert in alerts) {
          alertTypes.add(alert.alertTypeId);
        }
      }

      final tasks = linked.length;

      // Grab the first PunchCard in this link-group to determine displayed job/location
      final firstPC = linked.first;
      final jobTypeName = jobTypeMap[firstPC.jobTypeId]?.name ?? '';
      final location = locationFromPunchCard(firstPC);
      final locationName = location?.name ?? '';

      result.add(_PunchCardGroup(
        punchCardLinkId: entry.key,
        firstPunchCardId: firstPC.id,
        closedDuration: closedDuration,
        openClockIns: openClockIns,
        isLive: isLive,
        jobTypeName: jobTypeName,
        locationName: locationName,
        earliestClockIn: earliest,
        latestClockOut: latest,
        alertCount: alertsCount,
        alertTypes: alertTypes,
        taskCount: tasks,
      ));
    }
    return result;
  }


  Future<void> setSelectedEmployeeId(String id) async {
    // Only refresh if the ID has changed
    if (selectedEmployeeId != id) {
      selectedEmployeeId = id;

      // Update selectedPunchCards immediately based on the new employee ID
      if (employeeMap.containsKey(id)) {
        selectedPunchCards = punchCards.where((pc) => pc.userId == id);

        // Update grouped punch cards
        groupedPunchCards = _groupPunchCardsByLinkId(selectedPunchCards);

        // Notify listeners to update the UI
        notifyListeners();
      }

      // Then do a full refresh to ensure all data is up to date
      await refresh();
    }
  }

  /// If user picks a single day (start == end), we interpret that as midnight..23:59.
  /// Otherwise we do date-based times for the entire range.
  Future<void> setTimePeriod(DateTime from, DateTime to) async {
    // If single day, cover midnight..23:59
    if (from.isAtSameMomentAs(to)) {
      start = DateTime(from.year, from.month, from.day, 0, 0, 0);
      end = DateTime(to.year, to.month, to.day, 23, 59, 59);
    } else {
      // Full days
      start = DateTime(from.year, from.month, from.day, 0, 0, 0);
      end = DateTime(to.year, to.month, to.day, 23, 59, 59);
    }
    await refresh();
  }

  Future<void> setTimePeriodAll() async {
    final all = await DataModel().punchCardModel.all;
    if (all.isEmpty) {
      // no data at all
      start = DateTime.now().dateOnly;
      end = DateTime.now().dateOnly.add(const Duration(days: 1));
      await refresh();
      return;
    }
    // earliest clockIn
    final earliest = all
        .map((pc) => pc.clockedIn)
        .reduce((a, b) => a.isBefore(b) ? a : b);
    // latest clockIn
    final latest = all
        .map((pc) => pc.clockedIn)
        .reduce((a, b) => a.isAfter(b) ? a : b);

    start = DateTime(
      earliest.year,
      earliest.month,
      earliest.day,
      0,
      0,
      0,
    );
    end = DateTime(
      latest.year,
      latest.month,
      latest.day,
      23,
      59,
      59,
    );
    await refresh();
  }

  bool get isRefreshed => true;

  Location? locationFromPunchCard(PunchCard pc) {
    if (pc.locationId != null && locationMap.containsKey(pc.locationId)) {
      return locationMap[pc.locationId];
    }
    if (pc.scheduleId != null) {
      final sch = scheduleMap[pc.scheduleId];
      if (sch != null && sch.locationId != null) {
        return locationMap[sch.locationId];
      }
    }
    return null;
  }
}
